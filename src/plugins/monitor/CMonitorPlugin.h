//
//  CMonitorPlugin.hpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CMonitorPlugin_hpp
#define CMonitorPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include <memory>

class CMonitorPlugin : public IPlugin
{
public:
    CMonitorPlugin();
    virtual ~CMonitorPlugin();
    virtual PluginInfo* info() override;

    virtual void control(int msgID, const json& controlData) override;
    virtual json telemetry() override;

    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;

    void set_plugins(const map<string, IPlugin*>& plugins) { m_plugins = plugins; }
    void set_dispatcher(IPlugin* dispatcher) { m_dispatcher = dispatcher; }

    void start_telemetry_collector();
    void collect_telemetry_data();

private:
    bool m_running;
    pthread_t m_telemetry_thread;
    map<string, IPlugin*> m_plugins;
    map<string, json> m_telemetry_data;
    IPlugin* m_dispatcher;

    static void* telemetry_collector_thread(void* arg);
};

#endif /* DemoMonitor_hpp */
