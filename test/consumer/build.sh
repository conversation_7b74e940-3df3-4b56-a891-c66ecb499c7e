#!/bin/bash

set -e

echo "🔨 Building pangu Consumer (Rust)..."

if ! command -v cargo &> /dev/null; then
    echo "❌ Cargo not found. Please install Rust first."
    echo "Visit: https://rustup.rs/"
    exit 1
fi

echo "📦 Building release version..."
cargo build --release

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📍 Executable location: target/release/consumer"
    echo ""
    echo "🚀 Usage examples:"
    echo "  ./target/release/consumer                    # Basic usage"
    echo "  ./target/release/consumer -d                 # Detailed mode"
    echo "  ./target/release/consumer -i 50              # 50ms polling interval"
    echo "  ./target/release/consumer -m 100             # Process max 100 events"
    echo "  ./target/release/consumer -q                 # Quiet mode"
else
    echo "❌ Build failed!"
    exit 1
fi
