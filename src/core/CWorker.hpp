//
//  CWorker.hpp
//  pangu
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef CWorker_h
#define CWorker_h

#include "common/common.h"
#include "core/Queue.hpp"
#include "core/ITasker.h"

using namespace std;

class CWorker
{
   public:
    CWorker() : m_tasker(nullptr), m_queue(nullptr), m_downstream_queue(nullptr), m_thread_running(false)
    {
        if (m_worker_thread.joinable())
        {
            LOG_WARN("Worker 线程已经在运行");
            return;
        }

        m_worker_thread = std::thread(&CWorker::handle, this);
        LOG_INFO("Worker 处理线程创建成功，等待启动信号");
    }

    virtual ~CWorker()
    {
        LOG_INFO("delete CWorker");
        stop();
        if (m_worker_thread.joinable())
        {
            m_worker_thread.join();
            LOG_INFO("Worker 处理线程已停止");
        }
        m_tasker = nullptr;
    }

    void setup(Queue* queue, Queue* downstreamQueue)
    {
        m_queue = queue;
        m_downstream_queue = downstreamQueue;
        LOG_INFO("Worker 队列设置完成: queue=" << queue << ", downstream=" << downstreamQueue);
    }

    void setup(ITasker* tasker)
    {
        m_tasker = tasker;
        LOG_INFO("Worker Tasker 设置完成: " << tasker);

        if (m_tasker)
        {
            LOG_INFO("Worker 设置完成，等待手动启动");
        }
        else
        {
            LOG_WARN("Worker 设置不完整，缺少队列或Tasker");
        }
    }

    void start()
    {
        if (!m_tasker)
        {
            LOG_INFO("Worker 没有 Tasker，跳过消息处理（适用于控制流插件）");
            return;
        }

        m_thread_running = true;
        LOG_INFO("Worker 处理线程已启动");
    }

    void stop()
    {
        m_thread_running = false;
        LOG_INFO("Worker 停止处理消息");
    }

    void handle()
    {
        LOG_INFO("Worker 处理线程开始运行，等待启动信号");

        while (!m_thread_running)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        LOG_INFO("Worker 收到启动信号，开始处理消息");

        while (m_thread_running)
        {
            void* msg = nullptr;
            int result = m_queue->consume(&msg);

            if (result == QUEUE_OK && msg != nullptr)
            {
                try
                {
                    void* processedMsg = m_tasker->execute(msg);

                    if (processedMsg && m_downstream_queue)
                    {
                        m_downstream_queue->produce(processedMsg);
                    }
                }
                catch (...)
                {
                    LOG_ERROR("Worker 处理线程处理消息时发生异常");
                }
            }
            else
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }

        LOG_INFO("Worker 处理线程结束运行");
    }

   private:
    ITasker* m_tasker;

    Queue* m_queue;             // 处理队列（用来消费和处理）
    Queue* m_downstream_queue;  // 下游队列（用于向下游传递消息）

    // 线程管理 - Worker拥有处理线程
    std::thread m_worker_thread;  // 工作线程
    bool m_thread_running;        // 线程运行状态
};

#endif /* CWorker_h */
