# pangu高性能插件化数据处理框架 - 架构设计文档

## 文档概述

本文档基于4+1架构视图模型，全面描述pangu高性能插件化数据处理框架的架构设计。pangu是一个基于C++的高性能数据处理框架，采用Queue-Worker-Tasker架构模式，通过无锁队列实现插件间通信，专注于网络数据包捕获、协议解析和实时数据处理。

### 4+1架构视图概览

- **逻辑视图 (Logical View)**: 系统功能分解和模块关系
- **进程视图 (Process View)**: 运行时进程和线程架构
- **开发视图 (Development View)**: 代码组织和构建结构
- **物理视图 (Physical View)**: 部署架构和硬件映射
- **场景视图 (Scenario View)**: 关键用例和业务流程

---

# 1. 场景视图 (Scenario View)

场景视图描述系统的核心用例和业务流程，展示不同角色如何与pangu框架交互完成数据处理任务。

## 1.1 系统角色定义

### 主要角色
- **系统管理员 (Admin)**: 负责框架部署、配置管理、系统启停
- **开发人员 (Developer)**: 负责插件开发、系统调试、性能优化
- **运维人员 (Operator)**: 负责日常监控、故障处理、性能调优
- **数据分析师 (Analyst)**: 使用处理后的数据进行业务分析

### 外部系统
- **网络数据源**: 提供原始网络数据包的网络接口
- **共享内存系统**: 接收处理后数据的存储系统
- **监控系统**: 外部监控和告警平台

## 1.2 核心用例图

```mermaid
graph TB
    %% 参与者定义
    Admin((系统管理员))
    Developer((开发人员))
    Operator((运维人员))
    Network((网络数据源))
    Storage((共享内存系统))

    %% 核心用例定义
    subgraph UseCases["pangu框架核心用例"]
        UC1[系统启动管理]
        UC2[数据处理流水线]
        UC3[插件生命周期管理]
        UC4[实时监控告警]
        UC5[配置动态更新]
        UC6[性能调优分析]
    end

    %% 关系定义
    Admin --> UC1
    Admin --> UC3
    Admin --> UC5
    Developer --> UC3
    Developer --> UC6
    Operator --> UC4
    Operator --> UC6
    Network --> UC2
    UC2 --> Storage

    %% 用例间关系
    UC1 -.->|includes| UC3
    UC2 -.->|triggers| UC4
    UC5 -.->|affects| UC3

    %% 样式定义
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class Admin,Developer,Operator actor
    class UC1,UC2,UC3,UC4,UC5,UC6 usecase
    class Network,Storage system
```

## 1.3 关键业务场景

### 场景1: 实时HTTP数据包处理

```mermaid
sequenceDiagram
    participant Network as 网络接口
    participant Source as Source插件
    participant Parser as Parser插件
    participant Upload as Upload插件
    participant SharedMem as 共享内存
    participant Monitor as Monitor插件

    Note over Network, Monitor: 典型HTTP数据包处理流程

    Network->>Source: 网络数据包
    Note right of Network: libpcap捕获数据包

    Source->>Source: 数据包预处理和过滤
    Source->>Parser: NetworkPacketData(msgID=11)
    Note right of Source: 通过无锁队列传递

    Parser->>Parser: HTTP协议解析
    Parser->>Parser: TCP流重组
    Parser->>Upload: HTTPParseResult(msgID=21)
    Note right of Parser: 解析完成的HTTP请求

    Upload->>Upload: 数据格式化为JSON
    Upload->>SharedMem: 写入共享内存队列
    SharedMem->>Upload: 确认写入成功
    Note right of Upload: 数据持久化完成

    par 并行监控收集
        Monitor->>Source: telemetry()调用
        Source->>Monitor: 返回性能指标
    and
        Monitor->>Parser: telemetry()调用
        Parser->>Monitor: 返回处理统计
    and
        Monitor->>Upload: telemetry()调用
        Upload->>Monitor: 返回上传状态
    end

    Note over Network, Monitor: 特点：异步处理，实时监控，高性能传输
```

### 场景2: 系统启动和插件初始化

```mermaid
sequenceDiagram
    participant Main as 主程序
    participant PluginMgr as 插件管理器
    participant Source as Source插件
    participant Parser as Parser插件
    participant Upload as Upload插件
    participant Monitor as Monitor插件
    participant Dispatcher as Dispatcher插件

    Note over Main, Dispatcher: 系统启动和初始化流程

    Main->>PluginMgr: dlopen加载插件管理器
    PluginMgr->>PluginMgr: 扫描plugins目录
    PluginMgr->>PluginMgr: 加载.so/.dylib文件

    par 插件初始化(Queue-Worker-Tasker)
        PluginMgr->>Source: init()
        Source->>Source: 创建CSourceTasker
        Source->>Source: 创建数据捕获线程
        Source->>PluginMgr: 初始化完成
    and
        PluginMgr->>Parser: init()
        Parser->>Parser: 创建CParserTasker
        Parser->>Parser: 设置Worker线程
        Parser->>PluginMgr: 初始化完成
    and
        PluginMgr->>Upload: init()
        Upload->>Upload: 创建CUploadTasker
        Upload->>Upload: 初始化共享内存队列
        Upload->>PluginMgr: 初始化完成
    end

    PluginMgr->>PluginMgr: 建立插件间队列连接
    PluginMgr->>Dispatcher: 注册控制消息路由表

    par 插件启动
        PluginMgr->>Source: start()
        Source->>Source: 启动libpcap数据捕获
    and
        PluginMgr->>Parser: start()
        Parser->>Parser: Worker开始消息处理
    and
        PluginMgr->>Upload: start()
        Upload->>Upload: Worker开始消息处理
    end

    PluginMgr->>Monitor: 开始遥测数据收集
    Note over Main, Dispatcher: 系统进入运行状态，开始数据处理
```

### 场景3: 动态控制和配置更新

```mermaid
sequenceDiagram
    participant User as 用户/运维
    participant Interactor as Interactor插件
    participant Dispatcher as Dispatcher插件
    participant Target as 目标插件

    Note over User, Target: 控制命令处理流程

    User->>Interactor: 发送控制命令
    Note right of User: 通过命名管道或Web接口

    Interactor->>Interactor: 解析JSON控制消息
    Interactor->>Dispatcher: 直接调用control(msgID, data)
    Note right of Interactor: 直接方法调用，无中间消息结构

    Dispatcher->>Dispatcher: 查找路由表订阅者
    Dispatcher->>Target: 直接调用control(msgID, data)
    Note right of Dispatcher: 同步调用，非队列传递

    Target->>Target: 执行控制逻辑
    Target->>Target: 更新内部状态
    Target->>Dispatcher: 返回执行结果
    Dispatcher->>Interactor: 转发操作结果
    Interactor->>User: 返回JSON响应

    Note over User, Target: 特点：同步控制，实时响应，配置热更新
```

## 1.4 质量属性场景

### 性能场景
- **场景描述**: 高并发网络数据包实时处理
- **环境条件**: 千兆网络环境，多核CPU服务器
- **刺激源**: 每秒处理10万个网络数据包
- **预期响应**: 平均处理延迟<1ms，内存使用稳定，CPU使用率<80%
- **架构机制**: 无锁环形队列、异步处理、内存预分配

### 可靠性场景
- **场景描述**: 单个插件异常不影响整体系统稳定性
- **环境条件**: 系统正常运行状态
- **刺激源**: Parser插件发生内存访问异常
- **预期响应**: 异常被捕获，插件自动重启，数据流继续处理
- **架构机制**: 插件进程隔离、异常捕获机制、健康检查

### 可扩展性场景
- **场景描述**: 动态添加新的协议解析插件
- **环境条件**: 系统运行中，需要支持新协议
- **刺激源**: 业务需求变更，需要解析MQTT协议
- **预期响应**: 无需停机，动态加载新插件，配置路由即可使用
- **架构机制**: 插件化架构、标准接口、配置驱动路由

---

# 2. 逻辑视图 (Logical View)

逻辑视图描述系统的功能分解和模块关系，展示pangu框架的核心抽象和设计模式。

## 2.1 系统整体架构

### 核心架构模式

pangu框架采用**Queue-Worker-Tasker**三层架构模式，结合插件化设计实现高性能数据处理：

```mermaid
graph TB
    subgraph "pangu框架核心架构"
        subgraph "插件层 (Plugin Layer)"
            IPlugin[IPlugin基类]
            SourcePlugin[Source插件]
            ParserPlugin[Parser插件]
            UploadPlugin[Upload插件]
            MonitorPlugin[Monitor插件]
            DispatcherPlugin[Dispatcher插件]
            InteractorPlugin[Interactor插件]
        end

        subgraph "处理层 (Processing Layer)"
            Queue[Queue无锁队列]
            Worker[Worker消息处理]
            Tasker[Tasker业务逻辑]
        end

        subgraph "核心层 (Core Layer)"
            PluginManager[插件管理器]
            MessageRouter[消息路由器]
            ConfigManager[配置管理器]
        end
    end

    %% 关系定义
    IPlugin --> Queue
    IPlugin --> Worker
    IPlugin --> Tasker

    SourcePlugin -.-> IPlugin
    ParserPlugin -.-> IPlugin
    UploadPlugin -.-> IPlugin
    MonitorPlugin -.-> IPlugin
    DispatcherPlugin -.-> IPlugin
    InteractorPlugin -.-> IPlugin

    PluginManager --> IPlugin
    MessageRouter --> Queue
    ConfigManager --> IPlugin
```

## 2.2 核心模块设计

### Queue-Worker-Tasker架构模式

```mermaid
classDiagram
    %% 核心框架类
    class IPlugin {
        <<interface>>
        +init() void
        +start() void
        +stop() void
        +uninit() void
        +info() PluginInfo*
        +control(msgID, data) void
        +telemetry() json
        #m_queue Queue*
        #m_worker CWorker*
        #m_tasker ITasker*
    }

    class CWorker {
        +setup(queue, downstream) void
        +setup(tasker) void
        +start() void
        +stop() void
        +handle() void
        -m_queue Queue*
        -m_downstream_queue Queue*
        -m_tasker ITasker*
        -m_thread_running bool
    }

    class Queue {
        +produce(val) int
        +consume(val) int
        +elements() int
        +flush() int
        -data void*[QUEUE_MAX_NUM]
        -m_prod QueueCursor
        -m_cons QueueCursor
    }

    class ITasker {
        <<interface>>
        +execute(msg) void*
        +clear() void
    }

    %% 关系定义
    IPlugin *-- CWorker : 包含
    IPlugin *-- Queue : 包含
    IPlugin *-- ITasker : 包含
    CWorker --> Queue : 使用
    CWorker --> ITasker : 调用
```

### 插件类层次结构

```mermaid
classDiagram
    %% 插件基类
    class IPlugin {
        <<interface>>
        +init() void
        +start() void
        +stop() void
        +uninit() void
        +info() PluginInfo*
        +control(msgID, data) void
        +telemetry() json
    }

    %% 业务插件
    class CSourcePlugin {
        +init() void
        +start() void
        +control(msgID, data) void
        +telemetry() json
        -packet_capture_loop() void
        -m_capture_thread thread
        -m_isRunning bool
    }

    class CParserPlugin {
        +init() void
        +start() void
        +control(msgID, data) void
        +telemetry() json
    }

    class CUploadPlugin {
        +init() void
        +start() void
        +control(msgID, data) void
        +telemetry() json
        -m_shared_queue ShmQueue*
    }

    %% 系统插件
    class CMonitorPlugin {
        +init() void
        +start() void
        +control(msgID, data) void
        +telemetry() json
        +set_dispatcher(dispatcher) void
        -m_dispatcher CDispatcherPlugin*
        -m_running atomic_bool
        -m_telemetry_thread pthread_t
    }

    class CDispatcherPlugin {
        +init() void
        +start() void
        +stop() void
        +control(msgID, data) void
        +telemetry() json
        +set_plugins(plugins) void
        +buildRoutingTable() void
        -m_plugin_map map<string, IPlugin*>
        -m_routing_table map<int, list<IPlugin*>>
    }

    class CInteractorPlugin {
        +init() void
        +start() void
        +stop() void
        +control(msgID, data) void
        +telemetry() json
        +set_dispatcher(dispatcher) void
        -m_dispatcher CDispatcherPlugin*
        -m_running atomic_bool
        -m_should_exit atomic_bool
        -m_tasker CInteractorTasker*
        -m_tasker_thread thread
    }

    %% 继承关系
    IPlugin <|-- CSourcePlugin
    IPlugin <|-- CParserPlugin
    IPlugin <|-- CUploadPlugin
    IPlugin <|-- CMonitorPlugin
    IPlugin <|-- CDispatcherPlugin
    IPlugin <|-- CInteractorPlugin
```
### Tasker业务逻辑层

```mermaid
classDiagram
    %% Tasker接口
    class ITasker {
        <<interface>>
        +execute(msg) void*
        +clear() void
    }

    %% 业务Tasker实现
    class CSourceTasker {
        +execute(msg) void*
        +clear() void
        +getNextPacket() NetworkPacketData*
        +parseEthernetHeader() bool
        +parseIPHeader() bool
        +parseTCPHeader() bool
        -m_handle pcap_t*
        -m_stats CaptureStats
        -m_deviceName string
        -m_filterExpression string
    }

    class CParserTasker {
        +execute(msg) void*
        +clear() void
        +processNetworkPacket() HTTPParseResult*
        +isSimpleHTTPRequest() bool
        +extractHTTPInfo() bool
        +createSimpleHTTPResult() HTTPParseResult*
        -m_tcpFlows map
    }

    class CUploadTasker {
        +execute(msg) void*
        +clear() void
        +createHTTPUploadEvent() json
        +writeToSharedMemory() bool
        +generateEventId() string
        +getCurrentTimeString() string
        -m_shared_queue ShmQueue*
    }

    %% 系统Tasker实现
    class CMonitorTasker {
        +execute(msg) void*
        +clear() void
        +collect_telemetry_data() void
        -m_dispatcher CDispatcherPlugin*
        -m_telemetry_data json
    }

    %% 注：CDispatcherPlugin不使用Tasker，直接通过control方法调用

    class CInteractorTasker {
        +execute(msg) void*
        +clear() void
        +open_pipe() void
        +close_pipe() void
        -m_pipe_path string
        -m_pipe_fd int
        -m_dispatcher CDispatcherPlugin*
    }

    %% 继承关系
    ITasker <|-- CSourceTasker
    ITasker <|-- CParserTasker
    ITasker <|-- CUploadTasker
    ITasker <|-- CMonitorTasker
    ITasker <|-- CInteractorTasker
```

## 2.3 消息系统设计

### 重要说明：控制流实现机制

**实际实现与理论设计的差异**：
- 文档中提到的`ControlPayload`结构体在实际代码中**不存在**
- 控制消息的传递采用**直接方法调用**而非消息队列机制
- Interactor插件通过`CInteractorTasker`读取命名管道中的JSON命令
- 解析后直接调用`CDispatcherPlugin::control(int msgID, const json& data)`方法
- Dispatcher插件查找路由表后直接调用目标插件的`control()`方法
- 整个控制流程是**同步的**，无需中间消息结构

### 消息类型定义

```mermaid
classDiagram
    %% 数据流消息
    class NetworkPacketData {
        +packetData void*
        +packetSize size_t
        +timestamp int
        +srcIP string
        +dstIP string
        +protocol string
        +srcPort int
        +dstPort int
        +srcMAC string
        +dstMAC string
        +interface string
    }

    class HTTPParseResult {
        +srcIP string
        +dstIP string
        +srcPort int
        +dstPort int
        +timestamp int
        +method string
        +url string
        +path string
        +query string
        +httpVersion string
        +statusCode int
        +statusMessage string
        +headers string
        +body string
        +contentLength size_t
        +isComplete bool
        +isValid bool
    }

    %% 控制流消息（注：实际实现中不存在ControlPayload结构体）
    %% 控制消息通过直接的方法调用传递：control(msgID, json data)

    %% 消息ID枚举
    class DataMsgID {
        <<enumeration>>
        MSG_DATA_SOURCE_TO_PARSER = 11
        MSG_DATA_PARSER_TO_UPLOAD = 21
    }

    class ControlMsgID {
        <<enumeration>>
        MSG_CONTROL_SOURCE = 301
        MSG_CONTROL_PARSER = 302
        MSG_CONTROL_UPLOAD = 303
        MSG_CONTROL_ALL = 399
    }

    class TelemetryMsgID {
        <<enumeration>>
        MSG_TELEMETRY_REQUEST = 201
    }
```

### 消息路由机制

```mermaid
graph TB
    %% 数据流路由
    Source[Source插件] -->|NetworkPacketData<br/>msgID=11| Parser[Parser插件]
    Parser -->|HTTPParseResult<br/>msgID=21| Upload[Upload插件]

    %% 控制流路由
    Interactor[Interactor插件] -->|control方法调用| Dispatcher[Dispatcher插件]
    Dispatcher -->|control方法调用| TargetPlugin[目标插件]

    %% 监控数据收集
    Monitor[Monitor插件] -->|telemetry方法调用| AllPlugins[所有插件]
    AllPlugins -->|JSON遥测数据| Monitor

    %% 样式定义
    classDef sourceStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef parserStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef uploadStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef monitorStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dispatcherStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef interactorStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef targetStyle fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef allPluginsStyle fill:#f9fbe7,stroke:#827717,stroke-width:2px

    %% 应用样式
    class Source sourceStyle
    class Parser parserStyle
    class Upload uploadStyle
    class Monitor monitorStyle
    class Dispatcher dispatcherStyle
    class Interactor interactorStyle
    class TargetPlugin targetStyle
    class AllPlugins allPluginsStyle
```

---

# 3. 进程视图 (Process View)

进程视图描述系统的运行时架构，展示进程、线程的组织和交互方式。

## 3.1 进程架构概览

### 主进程架构

```mermaid
graph TB
    subgraph MainProcess["pangu主进程 pangu"]
        subgraph MainThreadGroup["主线程"]
            MainThread[主线程<br/>main函数]
            PluginMgr[插件管理器<br/>PluginManager]
        end

        subgraph ThreadPool["插件线程池"]
            SourceThread[Source数据捕获线程<br/>packet_capture_loop]
            WorkerThread1[Parser Worker线程<br/>CWorker handle]
            WorkerThread2[Upload Worker线程<br/>CWorker handle]
            MonitorThread[Monitor收集线程<br/>collect_telemetry_data]
            InteractorThread[Interactor交互线程<br/>CInteractorTasker execute]
        end

        subgraph SharedResources["共享资源"]
            Queue1[Source到Parser队列<br/>Queue]
            Queue2[Parser到Upload队列<br/>Queue]
            SharedMem[共享内存队列<br/>ShmQueue]
            ConfigData[配置数据<br/>JSON]
        end
    end

    %% 线程间关系
    MainThread --> PluginMgr
    PluginMgr --> SourceThread
    PluginMgr --> WorkerThread1
    PluginMgr --> WorkerThread2
    PluginMgr --> MonitorThread
    PluginMgr --> InteractorThread

    SourceThread --> Queue1
    WorkerThread1 --> Queue1
    WorkerThread1 --> Queue2
    WorkerThread2 --> Queue2
    WorkerThread2 --> SharedMem

    MonitorThread -.-> SourceThread
    MonitorThread -.-> WorkerThread1
    MonitorThread -.-> WorkerThread2

    %% 样式定义
    classDef mainStyle fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef threadStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef queueStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class MainThread,PluginMgr mainStyle
    class SourceThread,WorkerThread1,WorkerThread2,MonitorThread,InteractorThread threadStyle
    class Queue1,Queue2,SharedMem,ConfigData queueStyle
```

## 3.2 线程模型详解

### 数据处理线程流

```mermaid
sequenceDiagram
    participant Main as 主线程
    participant SourceT as Source线程
    participant WorkerT1 as Parser Worker线程
    participant WorkerT2 as Upload Worker线程
    participant MonitorT as Monitor线程
    participant Queue1 as Source->Parser队列
    participant Queue2 as Parser->Upload队列

    Note over Main, Queue2: 数据处理线程协作流程

    Main->>SourceT: 启动数据捕获线程
    Main->>WorkerT1: 启动Parser Worker
    Main->>WorkerT2: 启动Upload Worker
    Main->>MonitorT: 启动监控线程

    loop 数据处理循环
        SourceT->>SourceT: libpcap捕获数据包
        SourceT->>Queue1: produce(NetworkPacketData)
        Note right of SourceT: 无锁队列写入

        WorkerT1->>Queue1: consume(msg)
        Queue1->>WorkerT1: NetworkPacketData
        WorkerT1->>WorkerT1: HTTP协议解析
        WorkerT1->>Queue2: produce(HTTPParseResult)
        Note right of WorkerT1: 处理完成推送下游

        WorkerT2->>Queue2: consume(msg)
        Queue2->>WorkerT2: HTTPParseResult
        WorkerT2->>WorkerT2: 格式化为JSON
        WorkerT2->>WorkerT2: 写入共享内存
        Note right of WorkerT2: 数据持久化
    end

    par 并行监控
        MonitorT->>SourceT: 收集性能指标
        SourceT->>MonitorT: 返回统计数据
    and
        MonitorT->>WorkerT1: 收集处理统计
        WorkerT1->>MonitorT: 返回解析指标
    and
        MonitorT->>WorkerT2: 收集上传状态
        WorkerT2->>MonitorT: 返回上传统计
    end

    Note over Main, Queue2: 特点：异步处理，无锁通信，实时监控
```

### 控制流线程交互

```mermaid
sequenceDiagram
    participant User as 用户
    participant InteractorT as Interactor线程
    participant DispatcherT as Dispatcher线程
    participant TargetT as 目标插件线程
    participant Pipe as 命名管道

    Note over User, Pipe: 控制命令处理线程交互

    User->>Pipe: 写入控制命令JSON
    InteractorT->>Pipe: 读取命令数据
    InteractorT->>InteractorT: 解析JSON命令
    InteractorT->>DispatcherT: 直接调用control(msgID, data)

    DispatcherT->>DispatcherT: 查找路由表
    DispatcherT->>TargetT: 直接调用control()
    Note right of DispatcherT: 同步调用，非队列

    TargetT->>TargetT: 执行控制逻辑
    TargetT->>DispatcherT: 返回执行结果
    DispatcherT->>InteractorT: 转发结果
    InteractorT->>Pipe: 写入响应JSON
    User->>Pipe: 读取响应结果

    Note over User, Pipe: 特点：同步控制，实时响应，管道通信
```

## 3.3 并发控制机制

### 无锁队列并发模型

```mermaid
graph TB
    subgraph LockFreeQueue["无锁队列并发控制"]
        subgraph ProducerGroup["生产者线程"]
            Producer[生产者线程<br/>Source Thread]
            ProdHead[生产者头指针<br/>m_prod head]
            ProdTail[生产者尾指针<br/>m_prod tail]
        end

        subgraph ConsumerGroup["消费者线程"]
            Consumer[消费者线程<br/>Worker Thread]
            ConsHead[消费者头指针<br/>m_cons head]
            ConsTail[消费者尾指针<br/>m_cons tail]
        end

        subgraph RingBufferGroup["共享环形缓冲区"]
            RingBuffer[环形缓冲区<br/>data QUEUE_MAX_NUM]
            Slot0[slot 0]
            Slot1[slot 1]
            SlotN[slot N]
        end

        subgraph MemoryBarrierGroup["内存屏障"]
            ReadBarrier[读内存屏障<br/>spsc_read_acquire]
            WriteBarrier[写内存屏障<br/>spsc_write_release]
        end
    end

    %% 关系定义
    Producer --> ProdHead
    Producer --> ProdTail
    Consumer --> ConsHead
    Consumer --> ConsTail

    ProdHead -.-> RingBuffer
    ProdTail -.-> RingBuffer
    ConsHead -.-> RingBuffer
    ConsTail -.-> RingBuffer

    RingBuffer --> Slot0
    RingBuffer --> Slot1
    RingBuffer --> SlotN

    Producer -.-> WriteBarrier
    Consumer -.-> ReadBarrier

    %% 样式定义
    classDef producerStyle fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef consumerStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef bufferStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef barrierStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class Producer,ProdHead,ProdTail producerStyle
    class Consumer,ConsHead,ConsTail consumerStyle
    class RingBuffer,Slot0,Slot1,SlotN bufferStyle
    class ReadBarrier,WriteBarrier barrierStyle
```

### 线程同步策略

```mermaid
graph TB
    subgraph ThreadSync["线程同步机制"]
        subgraph DataSync["数据同步"]
            AtomicOps[原子操作<br/>CAS指令]
            MemoryBarrier[内存屏障<br/>acquire release]
            LockFree[无锁算法<br/>SPSC队列]
        end

        subgraph ControlSync["控制同步"]
            ThreadFlag[线程标志<br/>m_thread_running]
            ConditionVar[条件变量<br/>condition_variable]
            Mutex[互斥锁<br/>std mutex]
        end

        subgraph LifecycleSync["生命周期同步"]
            ThreadJoin[线程等待<br/>thread join]
            GracefulShutdown[优雅关闭<br/>信号处理]
            ResourceCleanup[资源清理<br/>RAII模式]
        end
    end

    %% 应用场景
    AtomicOps -.-> LockFree
    MemoryBarrier -.-> LockFree
    ThreadFlag -.-> ConditionVar
    ConditionVar -.-> Mutex
    ThreadJoin -.-> GracefulShutdown
    GracefulShutdown -.-> ResourceCleanup

    %% 样式定义
    classDef dataStyle fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef controlStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef lifecycleStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class AtomicOps,MemoryBarrier,LockFree dataStyle
    class ThreadFlag,ConditionVar,Mutex controlStyle
    class ThreadJoin,GracefulShutdown,ResourceCleanup lifecycleStyle
```

---

# 4. 开发视图 (Development View)

开发视图描述系统的代码组织结构、构建配置和开发环境。

## 4.1 代码组织结构

### 目录结构设计

```
pangu/
├── src/                          # 源代码目录
│   ├── common/                   # 公共组件
│   │   ├── common.h             # 通用头文件和宏定义
│   │   └── msg.h                # 消息结构定义
│   ├── core/                     # 核心框架
│   │   ├── IPlugin.h            # 插件接口定义
│   │   ├── ITasker.h            # 任务处理器接口
│   │   ├── CWorker.hpp          # Worker线程实现
│   │   ├── Queue.hpp            # 无锁队列实现
│   │   ├── ShmQueue.hpp         # 共享内存队列
│   │   ├── MessageManager.h     # 消息管理器
│   │   └── MessageManager.cpp
│   ├── plugins/                  # 插件实现
│   │   ├── framework/           # 框架插件
│   │   │   ├── PluginManager.h  # 插件管理器
│   │   │   ├── PluginManager.cpp
│   │   │   └── ModulePluginManager.cpp
│   │   ├── source/              # 数据源插件
│   │   │   ├── CSourcePlugin.h
│   │   │   ├── CSourcePlugin.cpp
│   │   │   ├── CSourceTasker.h
│   │   │   ├── CSourceTasker.cpp
│   │   │   └── ModuleSource.cpp
│   │   ├── parsers/             # 解析器插件
│   │   │   ├── CParserPlugin.h
│   │   │   ├── CParserPlugin.cpp
│   │   │   ├── CParserTasker.h
│   │   │   ├── CParserTasker.cpp
│   │   │   ├── HTTPProtocolParser.h
│   │   │   ├── HTTPProtocolParser.cpp
│   │   │   └── ModuleParser.cpp
│   │   ├── upload/              # 上传插件
│   │   │   ├── CUploadPlugin.h
│   │   │   ├── CUploadPlugin.cpp
│   │   │   ├── CUploadTasker.h
│   │   │   ├── CUploadTasker.cpp
│   │   │   └── ModuleUpload.cpp
│   │   ├── monitor/             # 监控插件
│   │   │   ├── CMonitorPlugin.h
│   │   │   ├── CMonitorPlugin.cpp
│   │   │   ├── CMonitorTasker.h
│   │   │   ├── CMonitorTasker.cpp
│   │   │   └── ModuleMonitor.cpp
│   │   ├── dispatcher/          # 分发器插件
│   │   │   ├── CDispatcherPlugin.h
│   │   │   ├── CDispatcherPlugin.cpp
│   │   │   └── ModuleDispatcher.cpp
│   │   ├── interactor/          # 交互插件
│   │   │   ├── CInteractorPlugin.h
│   │   │   ├── CInteractorPlugin.cpp
│   │   │   ├── CInteractorTasker.h
│   │   │   ├── CInteractorTasker.cpp
│   │   │   └── ModuleInteractor.cpp
│   │   └── meson.build          # 插件构建配置
│   ├── utils/                    # 工具类
│   │   └── utils.h              # 工具函数和宏
│   ├── 3rd/                      # 第三方库
│   │   └── json/                # nlohmann/json库
│   └── main.cpp                  # 主程序入口
├── config/                       # 配置文件
│   ├── source.json              # Source插件配置
│   ├── parser.json              # Parser插件配置
│   ├── upload.json              # Upload插件配置
│   ├── monitor.json             # Monitor插件配置
│   ├── dispatcher.json          # Dispatcher插件配置
│   └── interactor.json          # Interactor插件配置
├── test/                         # 测试代码
│   ├── consumer/                # Rust消费者测试
│   ├── consumer_python/         # Python消费者测试
│   └── *.py                     # 测试脚本
├── docs/                         # 文档
│   └── 架构设计.md              # 架构设计文档
├── meson.build                   # 主构建文件
└── README.md                     # 项目说明
```

## 4.2 构建系统设计

### Meson构建配置

```mermaid
graph TB
    subgraph "构建系统架构"
        subgraph "主构建文件"
            MainMeson[meson.build<br/>主配置文件]
            ProjectConfig[项目配置<br/>C++20标准]
            Dependencies[依赖管理<br/>libpcap, pthread, dl]
        end

        subgraph "插件构建"
            PluginMeson[src/plugins/meson.build<br/>插件构建配置]
            SharedLibs[动态库生成<br/>.so/.dylib文件]
            PluginTargets[插件目标<br/>7个插件库]
        end

        subgraph "部署配置"
            DeployTarget[部署目标<br/>create_deploy]
            ConfigCopy[配置文件复制<br/>config/*.json]
            SymLinks[符号链接创建<br/>plugin_*.so]
        end

        subgraph "编译选项"
            CppStandard[C++20标准<br/>cpp_std=c++20]
            WarningLevel[警告级别<br/>warning_level=3]
            CompilerFlags[编译参数<br/>-pthread等]
        end
    end

    %% 关系定义
    MainMeson --> ProjectConfig
    MainMeson --> Dependencies
    MainMeson --> PluginMeson

    PluginMeson --> SharedLibs
    SharedLibs --> PluginTargets

    MainMeson --> DeployTarget
    DeployTarget --> ConfigCopy
    DeployTarget --> SymLinks

    ProjectConfig --> CppStandard
    ProjectConfig --> WarningLevel
    ProjectConfig --> CompilerFlags
```

### 依赖关系管理

```mermaid
graph TB
    subgraph "依赖层次结构"
        subgraph "系统依赖"
            LibPcap[libpcap<br/>网络包捕获]
            PThread[pthread<br/>线程支持]
            DL[dl<br/>动态库加载]
            StdLib[C++标准库<br/>STL容器]
        end

        subgraph "第三方库"
            JsonLib[nlohmann/json<br/>JSON处理]
            MemMap[memmap2<br/>内存映射]
        end

        subgraph "内部模块"
            CoreModule[核心模块<br/>Queue, Worker, Tasker]
            CommonModule[公共模块<br/>消息定义, 工具类]
            PluginModule[插件模块<br/>各业务插件]
        end
    end

    %% 依赖关系
    PluginModule --> CoreModule
    PluginModule --> CommonModule
    CoreModule --> JsonLib
    CoreModule --> PThread

    PluginModule --> LibPcap
    PluginModule --> DL
    CommonModule --> StdLib
```

---

# 5. 物理视图 (Physical View)

物理视图描述系统的部署架构和硬件映射关系。

## 5.1 部署架构

### 单机部署模式

```mermaid
graph TB
    subgraph "物理服务器"
        subgraph "操作系统层"
            OS[Linux/macOS<br/>操作系统]
            Kernel[内核<br/>网络栈支持]
            FileSystem[文件系统<br/>配置和日志存储]
        end

        subgraph "运行时环境"
            Runtime[C++运行时<br/>libstdc++/libc++]
            SystemLibs[系统库<br/>libpcap, pthread]
            SharedMem[共享内存<br/>进程间通信]
        end

        subgraph "应用层"
            panguProcess[pangu主进程<br/>pangu可执行文件]
            PluginLibs[插件库<br/>.so/.dylib文件]
            ConfigFiles[配置文件<br/>JSON配置]
        end

        subgraph "网络接口"
            NetworkCard[网卡<br/>数据包捕获]
            LoopbackIf[回环接口<br/>本地测试]
        end
    end

    %% 部署关系
    panguProcess --> Runtime
    panguProcess --> PluginLibs
    panguProcess --> ConfigFiles

    Runtime --> SystemLibs
    SystemLibs --> OS
    OS --> Kernel

    panguProcess -.-> NetworkCard
    panguProcess -.-> LoopbackIf
    panguProcess -.-> SharedMem

    ConfigFiles --> FileSystem
    FileSystem --> OS
```

### 分布式部署模式

```mermaid
graph TB
    subgraph "数据采集节点"
        subgraph "采集服务器1"
            Source1[Source插件<br/>网卡1数据采集]
            Parser1[Parser插件<br/>协议解析]
        end

        subgraph "采集服务器2"
            Source2[Source插件<br/>网卡2数据采集]
            Parser2[Parser插件<br/>协议解析]
        end
    end

    subgraph "处理中心节点"
        subgraph "处理服务器"
            Upload[Upload插件<br/>数据汇聚上传]
            Monitor[Monitor插件<br/>全局监控]
            Dispatcher[Dispatcher插件<br/>控制分发]
        end

        subgraph "存储系统"
            SharedMemCluster[共享内存集群<br/>分布式存储]
            Database[数据库<br/>持久化存储]
        end
    end

    subgraph "管理节点"
        subgraph "管理服务器"
            WebUI[Web管理界面<br/>集中管理]
            Interactor[Interactor插件<br/>统一交互]
        end
    end

    %% 网络连接
    Source1 -.->|TCP/UDP| Upload
    Source2 -.->|TCP/UDP| Upload
    Parser1 -.->|HTTP/JSON| Upload
    Parser2 -.->|HTTP/JSON| Upload

    Upload --> SharedMemCluster
    Upload --> Database

    Monitor -.->|监控数据| WebUI
    Dispatcher -.->|控制命令| Source1
    Dispatcher -.->|控制命令| Source2

    Interactor --> Dispatcher
    WebUI --> Interactor
```

## 5.2 硬件资源映射

### 性能要求和硬件配置

```mermaid
graph TB
    subgraph "硬件资源配置"
        subgraph "CPU要求"
            CPUCores[CPU核心<br/>≥4核心]
            CPUFreq[CPU频率<br/>≥2.4GHz]
            CPUArch[CPU架构<br/>x86_64/ARM64]
        end

        subgraph "内存要求"
            RAM[内存容量<br/>≥8GB]
            SharedMemSize[共享内存<br/>≥1GB]
            CacheSize[缓存大小<br/>L3≥8MB]
        end

        subgraph "存储要求"
            DiskSpace[磁盘空间<br/>≥100GB]
            DiskType[磁盘类型<br/>SSD推荐]
            IOSpeed[IO速度<br/>≥500MB/s]
        end

        subgraph "网络要求"
            NetworkSpeed[网络带宽<br/>≥1Gbps]
            NetworkCard[网卡类型<br/>支持混杂模式]
            NetworkLatency[网络延迟<br/><1ms]
        end
    end

    subgraph "资源映射"
        subgraph "CPU映射"
            SourceCPU[Source线程<br/>CPU核心0]
            ParserCPU[Parser线程<br/>CPU核心1]
            UploadCPU[Upload线程<br/>CPU核心2]
            MonitorCPU[Monitor线程<br/>CPU核心3]
        end

        subgraph "内存映射"
            QueueMem[队列内存<br/>256MB]
            PluginMem[插件内存<br/>512MB]
            SystemMem[系统内存<br/>预留2GB]
        end
    end

    %% 映射关系
    CPUCores --> SourceCPU
    CPUCores --> ParserCPU
    CPUCores --> UploadCPU
    CPUCores --> MonitorCPU

    RAM --> QueueMem
    RAM --> PluginMem
    RAM --> SystemMem

    SharedMemSize --> QueueMem
    NetworkCard --> SourceCPU
    DiskType --> UploadCPU
```

## 5.3 部署配置管理

### 配置文件组织

```mermaid
graph TB
    subgraph "配置管理体系"
        subgraph "环境配置"
            DevConfig[开发环境<br/>config/dev/]
            TestConfig[测试环境<br/>config/test/]
            ProdConfig[生产环境<br/>config/prod/]
        end

        subgraph "插件配置"
            SourceConfig[source.json<br/>数据源配置]
            ParserConfig[parser.json<br/>解析器配置]
            UploadConfig[upload.json<br/>上传配置]
            MonitorConfig[monitor.json<br/>监控配置]
        end

        subgraph "系统配置"
            LogConfig[logging.json<br/>日志配置]
            NetworkConfig[network.json<br/>网络配置]
            SecurityConfig[security.json<br/>安全配置]
        end

        subgraph "部署脚本"
            InstallScript[install.sh<br/>安装脚本]
            StartScript[start.sh<br/>启动脚本]
            StopScript[stop.sh<br/>停止脚本]
            UpdateScript[update.sh<br/>更新脚本]
        end
    end

    %% 配置关系
    DevConfig --> SourceConfig
    TestConfig --> ParserConfig
    ProdConfig --> UploadConfig

    SourceConfig -.-> LogConfig
    ParserConfig -.-> NetworkConfig
    UploadConfig -.-> SecurityConfig

    InstallScript --> DevConfig
    StartScript --> TestConfig
    StopScript --> ProdConfig
    UpdateScript --> SystemConfig
```

---

# 6. 架构决策和权衡

## 6.1 关键架构决策

### 决策1: Queue-Worker-Tasker架构模式
- **决策内容**: 采用三层架构模式分离关注点
- **驱动因素**: 需要高性能、可扩展的数据处理能力
- **权衡考虑**:
  - ✅ 优点: 职责分离清晰，易于扩展和维护
  - ✅ 优点: 支持异步处理，提高系统吞吐量
  - ❌ 缺点: 增加了系统复杂度
  - ❌ 缺点: 需要更多的内存开销

### 决策2: 无锁队列通信机制
- **决策内容**: 使用SPSC无锁环形队列进行插件间通信
- **驱动因素**: 追求极致性能，减少线程同步开销
- **权衡考虑**:
  - ✅ 优点: 极高的性能，无锁竞争
  - ✅ 优点: 确定性的延迟特性
  - ❌ 缺点: 实现复杂，调试困难
  - ❌ 缺点: 仅支持单生产者单消费者模式

### 决策3: 插件化架构设计
- **决策内容**: 基于动态库的插件化架构
- **驱动因素**: 需要良好的可扩展性和模块化
- **权衡考虑**:
  - ✅ 优点: 高度可扩展，支持热插拔
  - ✅ 优点: 模块化开发，团队协作友好
  - ❌ 缺点: 动态库管理复杂
  - ❌ 缺点: 运行时依赖管理

## 6.2 性能优化策略

### 内存管理优化
- **零拷贝设计**: 数据在插件间传递时避免不必要的内存拷贝
- **内存池技术**: 预分配内存池减少动态内存分配开销
- **NUMA感知**: 在多NUMA节点系统上优化内存访问模式

### 并发优化策略
- **CPU亲和性**: 将关键线程绑定到特定CPU核心
- **缓存友好**: 数据结构设计考虑CPU缓存行对齐
- **批处理优化**: 批量处理数据减少系统调用开销

## 6.3 可靠性保障机制

### 故障隔离策略
- **插件隔离**: 插件异常不影响框架核心
- **资源隔离**: 限制插件资源使用防止资源耗尽
- **故障检测**: 实时健康检查和异常监控

### 恢复机制设计
- **自动重启**: 插件异常后自动重启恢复
- **状态保存**: 关键状态持久化支持快速恢复
- **降级策略**: 系统过载时的优雅降级机制

---

# 7. 总结

## 7.1 架构特点总结

pangu高性能插件化数据处理框架基于4+1架构视图的设计，具有以下核心特点：

### 🏗️ **架构优势**
- **高性能**: 无锁队列 + 异步处理，支持高并发数据处理
- **可扩展**: 插件化架构，支持动态扩展和热插拔
- **可维护**: 清晰的分层设计和职责分离
- **跨平台**: 支持Linux和macOS，使用现代C++20标准

### 🔧 **技术亮点**
- **Queue-Worker-Tasker**: 三层架构模式实现高效数据流处理
- **SPSC无锁队列**: 单生产者单消费者无锁环形队列
- **混合线程模型**: 数据驱动 + 事件驱动的混合处理模式
- **实时监控**: 内置遥测系统支持实时性能监控

### 📊 **应用场景**
- **网络数据包分析**: 实时捕获和解析网络流量
- **协议解析处理**: HTTP/HTTPS等协议的高性能解析
- **数据流处理**: 大规模数据的实时处理和转换
- **系统监控**: 实时系统状态监控和告警

## 7.2 未来演进方向

### 短期目标 (v2.1)
- 完善Web管理界面功能
- 增强监控和告警机制
- 支持更多网络协议解析
- 优化性能和稳定性

### 中期目标 (v2.2)
- 支持分布式部署架构
- 实现插件热更新机制
- 添加机器学习数据分析
- 支持云原生部署

### 长期目标 (v3.0)
- 构建完整的数据处理生态
- 支持流式计算和批处理
- 实现智能化运维管理
- 提供SaaS服务模式

## 7.3 架构价值体现

pangu框架通过精心设计的架构，在**性能**、**可扩展性**、**可维护性**三个维度达到了良好的平衡，为高性能数据处理场景提供了一个可靠、高效、易扩展的解决方案。框架的插件化设计使其能够适应不同的业务需求，而无锁队列和异步处理机制确保了在高并发场景下的优异性能表现。

---

# 8. 实际实现验证

## 8.1 代码结构验证

本文档已根据实际代码进行了验证和更新，主要修正包括：

### 🔧 **控制流机制修正**
- **移除了不存在的`ControlPayload`结构体**
- **明确了控制消息通过直接方法调用传递**
- **更新了Interactor和Dispatcher的交互方式**

### 📁 **目录结构更新**
- **补充了实际存在的Module*.cpp文件**
- **添加了HTTPProtocolParser相关文件**
- **更新了meson.build构建配置**

### 🔌 **插件实现细节**
- **CDispatcherPlugin不使用Tasker机制**
- **CInteractorPlugin使用独立线程运行Tasker**
- **各插件的control()和telemetry()方法实现**

## 8.2 实际插件列表

根据代码分析，pangu框架包含以下7个插件：

1. **PluginManager** - 插件管理器（框架核心）
2. **CSourcePlugin** - 数据源插件（网络包捕获）
3. **CParserPlugin** - 解析器插件（HTTP协议解析）
4. **CUploadPlugin** - 上传插件（数据上传到共享内存）
5. **CMonitorPlugin** - 监控插件（遥测数据收集）
6. **CDispatcherPlugin** - 分发器插件（控制消息路由）
7. **CInteractorPlugin** - 交互插件（命名管道交互）

## 8.3 消息流验证

### 数据流消息（通过队列）
- `NetworkPacketData` (msgID=11): Source → Parser
- `HTTPParseResult` (msgID=21): Parser → Upload

### 控制流消息（直接调用）
- JSON命令 → Interactor → Dispatcher → 目标插件
- 使用`control(int msgID, const json& data)`方法

### 监控流消息（直接调用）
- Monitor调用各插件的`telemetry()`方法收集数据

本文档确保了架构设计与实际实现的一致性，为开发者提供准确的技术参考。


