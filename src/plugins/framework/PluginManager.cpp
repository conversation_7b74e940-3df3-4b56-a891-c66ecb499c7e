//
//  PluginManager.cpp
//  pangu
//
//  Created by <PERSON> on 2023/6/8.
//

#include "common/common.h"
#include "plugins/framework/PluginManager.h"
#include "plugins/dispatcher/CDispatcherPlugin.h"
#include "plugins/interactor/CInteractorPlugin.h"
#include "plugins/monitor/CMonitorPlugin.h"
#include "common/msg.h" // For message struct definitions for delete
#include <set> // For std::set
#include "core/CWorker.hpp"

PluginManager::PluginManager() {
    m_info = new PluginInfo("./config/manager.json");
}

PluginManager::~PluginManager() {
    stop();
    unloadPlugins();
    delete m_info;
}

void PluginManager::init() {
    loadPlugins();
    initPlugins();
    initDI();
    initBus();
}

void PluginManager::start() {
    for (auto& pair : m_plugins) {
        IPlugin* plugin = pair.second;
        PluginInfo* info = plugin->info();

        plugin->start();
        printf("插件 %s 启动完成\n", info->_name.c_str());
    }
}

void PluginManager::stop() {
    // 停止所有插件
    for (auto& pair : m_plugins) {
        pair.second->stop();
    }
}

void PluginManager::loadPlugins() {
    const char *path = "./plugins";
    DIR *dp = opendir(path);
    if (dp == NULL) {
        perror("opendir");
        return;
    }
    struct dirent *entry;
    while ((entry = readdir(dp)) != NULL) {
        string fname = entry->d_name;
        if (fname.rfind("plugin_", 0) != 0 || fname.rfind(".so") != fname.size() - 3 || fname == "plugin_manager.so") continue;
        char fullpath[1024];
        snprintf(fullpath, sizeof(fullpath), "%s/%s", path, entry->d_name);
        struct stat fileStat;
        if (stat(fullpath, &fileStat) == -1) {
            perror("stat");
            continue;
        }
        if (!S_ISREG(fileStat.st_mode)) continue;
        void* handle = dlopen(fullpath, RTLD_NOW);
        if (!handle) {
            printf("无法加载插件: %s, 错误: %s\n", fullpath, dlerror());
            continue;
        }
        load_t load_func = (load_t) dlsym(handle, "load");
        if (!load_func) {
            printf("插件 %s 中找不到 load 函数\n", fullpath);
            dlclose(handle);
            continue;
        }
        IPlugin* plugins[256] = {0};
        load_func(plugins, sizeof(plugins));
        if (plugins[0]) {
            PluginInfo* info = plugins[0]->info();
            string key = info->_name + "-" + info->_module;
            m_plugins[key] = plugins[0];
        }
    }
    closedir(dp);
}

void PluginManager::unloadPlugins() {
    m_plugins.clear();
}

void PluginManager::initPlugins() {
    for (auto& pair : m_plugins) {
        pair.second->init();
    }
}



void PluginManager::initDI() {
    // 根据插件的数据流依赖关系建立队列连接
    printf("开始建立插件间的数据流连接...\n");

    // 第一步：构建消息ID到插件的映射表
    std::map<uint32_t, IPlugin*> inputMsgToPlugin;  // 消息ID -> 需要该消息作为输入的插件

    for (auto& pair : m_plugins) {
        IPlugin* plugin = pair.second;
        PluginInfo* info = plugin->info();

        // 记录每个插件需要的输入消息ID
        for (uint32_t inputMsgID : info->_dataflow_input) {
            inputMsgToPlugin[inputMsgID] = plugin;
        }
    }

    // 第二步：根据输出消息ID建立连接
    for (auto& pair : m_plugins) {
        IPlugin* plugin = pair.second;
        PluginInfo* info = plugin->info();

        printf("处理插件: %s\n", info->_name.c_str());

        // 遍历该插件的输出消息ID
        for (uint32_t outputMsgID : info->_dataflow_output) {
            printf("  输出消息ID: %u\n", outputMsgID);

            // 直接查找需要这个消息的下游插件
            auto it = inputMsgToPlugin.find(outputMsgID);
            if (it != inputMsgToPlugin.end()) {
                IPlugin* downstreamPlugin = it->second;
                PluginInfo* downstreamInfo = downstreamPlugin->info();

                printf("    -> 连接到下游插件: %s (消息ID: %u)\n",
                       downstreamInfo->_name.c_str(), outputMsgID);

                // 将下游插件的队列注册到当前插件
                plugin->setup(downstreamPlugin->getQueue());
            }
        }
    }

    // 第三步：为没有下游连接的插件（终端插件）设置Worker
    for (auto& pair : m_plugins) {
        IPlugin* plugin = pair.second;
        PluginInfo* info = plugin->info();

        // 如果插件有输入消息ID但没有输出消息ID，说明是终端插件
        if (!info->_dataflow_input.empty() && info->_dataflow_output.empty()) {
            printf("设置终端插件: %s (无下游连接)\n", info->_name.c_str());
            // 调用setup，传入nullptr作为下游队列
            plugin->setup(nullptr);
        }
    }

    printf("数据流连接建立完成\n");
}

void PluginManager::initBus() {
    // 将dispatcher注入到各个插件中，用于处理控制和遥测消息
    auto dispatcherIt = m_plugins.find("dispatcher-dispatcher");
    if (dispatcherIt != m_plugins.end()) {
        IPlugin* dispatcher = dispatcherIt->second;

        // dispatcher维护所有插件的map，实现消息总线功能
        ((CDispatcherPlugin*)dispatcher)->set_plugins(m_plugins);

        // 初始化dispatcher，让它处理控制消息(301-303)和遥测消息(201-203)
        dispatcher->init();

        // 将dispatcher注入到monitor插件中
        auto monitorIt = m_plugins.find("monitor-monitor");
        if (monitorIt != m_plugins.end()) {
            ((CMonitorPlugin*)monitorIt->second)->set_dispatcher(dispatcher);
        }

        // 将dispatcher注入到interactor插件中
        auto interactorIt = m_plugins.find("interactor-interactor");
        if (interactorIt != m_plugins.end()) {
            ((CInteractorPlugin*)interactorIt->second)->set_dispatcher((CDispatcherPlugin*)dispatcher);
        }
    }
}

PluginInfo* PluginManager::info() {
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列